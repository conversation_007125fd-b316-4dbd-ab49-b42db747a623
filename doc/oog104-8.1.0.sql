-- 新增dish相关表
CREATE TABLE `proj_fitness_dish` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                             `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'name',
                             `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                             `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                             `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                             `types` varchar(255) DEFAULT NULL COMMENT '类型多选用英文逗号分隔，100:Breakfast,101:Lunch,102:Dinner,103:Meal Replacement',
                             `styles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '风格，多选用英文逗号分隔100:Vegan,101:Mediterranean,102:Keto,103:Smoothie',
                             `prepare_time` int DEFAULT NULL COMMENT '准备时间，单位分钟',
                             `calorie` decimal(8,1) DEFAULT '0.0' COMMENT '卡路里',
                             `carb` decimal(8,1) DEFAULT '0.0' COMMENT '碳水含量',
                             `protein` decimal(8,1) DEFAULT '0.0' COMMENT '蛋白质含量',
                             `fat` decimal(8,1) DEFAULT '0.0' COMMENT '脂肪含量',
                             `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
                             `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
                             `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
                             `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
                             `serving` int DEFAULT NULL COMMENT '份数',
                             `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                             `proj_id` int unsigned NOT NULL COMMENT '项目id',
                             `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish';


CREATE TABLE `proj_fitness_ingredient` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                   `amount` varchar(127) DEFAULT NULL,
                                   `proj_fitness_unit_id` int DEFAULT NULL COMMENT 'proj_fitness_unit表数据id',
                                   `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                   `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                   `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness ingredient';

CREATE TABLE `proj_fitness_unit` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                             `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
                             `proj_id` int unsigned NOT NULL COMMENT '项目id',
                             `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness_unit表';

CREATE TABLE `proj_fitness_dish_step` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `description` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                                  `proj_fitness_dish_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                  `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_proj_fitness_dish_id` (`proj_fitness_dish_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish step';

CREATE TABLE `proj_fitness_dish_step_tip` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                      `proj_fitness_dish_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                      `proj_fitness_dish_step_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish_step表数据id',
                                      `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
                                      `intro` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '介绍',
                                      `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_proj_fitness_dish_id` (`proj_fitness_dish_id`),
                                      KEY `idx_proj_fitness_dish_step_id` (`proj_fitness_dish_step_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish step tip';

CREATE TABLE `proj_fitness_allergen` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                 `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                 `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '过敏源名称',
                                 `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                 `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                 `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Allergen';

CREATE TABLE `proj_fitness_allergen_relation` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `data_id` int NOT NULL COMMENT '业务表数据id',
                                          `proj_fitness_allergen_id` int NOT NULL COMMENT 'proj_fitness_allergen表数据id',
                                          `business_type` int NOT NULL COMMENT '业务type，1000:dish',
                                          `proj_id` int NOT NULL COMMENT '项目id',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='fitness_allergen和业务表的关系';

-- 新增Fasting_article相关表
CREATE TABLE `proj_fitness_fasting_article` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                        `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                        `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                        `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                        `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                        `type` int DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                        `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                        `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                        `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                        `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                        `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                        `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                        `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                        `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Fasting article';


-- video course 相关表
CREATE TABLE `proj_fitness_video_course` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
     `table_code` tinyint DEFAULT NULL COMMENT '表标识',
     `proj_id` int unsigned NOT NULL COMMENT '项目id',
     `name` varchar(100) DEFAULT NULL COMMENT '名称',
     `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
     `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
     `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
     `play_type` int DEFAULT NULL COMMENT '视频宽高比',
     `type` int DEFAULT NULL COMMENT 'Course类型',
     `difficulty` int DEFAULT NULL COMMENT '难度',
     `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
     `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
     `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
     `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
     `calorie` int DEFAULT NULL COMMENT '卡路里',
     `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
     `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
     `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
     `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
     `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
     `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_video_course';

CREATE TABLE `proj_fitness_coach` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `proj_id` int NOT NULL COMMENT 'project id',
    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'coach name',
    `photo_img_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'photo_img_url',
    `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'introduction',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `proj_id` (`proj_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='proj_fitness_coach';


CREATE TABLE `proj_fitness_coaching_courses` (
   `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
   `table_code` tinyint DEFAULT NULL COMMENT '表标识',
   `proj_id` int unsigned NOT NULL COMMENT '项目id',
   `name` varchar(100) DEFAULT NULL COMMENT '名称',
   `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
   `cover_img_url` varchar(255) DEFAULT NULL COMMENT '封面图',
   `detail_img_url` varchar(255) DEFAULT NULL COMMENT '详情图',
   `age_groups` varchar(255) DEFAULT NULL COMMENT 'age_groups',
   `types` varchar(255) DEFAULT NULL COMMENT 'types',
   `difficulty` int DEFAULT NULL COMMENT '难度',
   `description` varchar(1000) DEFAULT NULL COMMENT '描述',
   `proj_fitness_coach_id` int DEFAULT NULL COMMENT '教练id',
   `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
   `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
   `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
   `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
   `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
   `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
   `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
   `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_coaching_courses';


CREATE TABLE `proj_fitness_coaching_courses_relation` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `proj_fitness_video_course_id` int unsigned DEFAULT NULL COMMENT 'video course id',
    `proj_fitness_coaching_courses_id` int unsigned DEFAULT NULL COMMENT 'coaching_courses_id',
    `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_coaching_courses_relation';

CREATE TABLE IF NOT EXISTS `proj_fitness_meal_plan` (
                                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                        `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `days` int DEFAULT NULL COMMENT '难度',
    `calorie` decimal(8,1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness meal plan ';

CREATE TABLE IF NOT EXISTS `proj_fitness_meal_plan_relation` (
                                                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                 `proj_fitness_meal_plan_id` int NOT NULL COMMENT 'proj_fitness_meal_plan id',
                                                                 `day` int NOT NULL COMMENT '表示一个MealPlan中第几天',
                                                                 `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
                                                                 `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish id',
                                                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                 `proj_id` int NOT NULL COMMENT '项目id',
                                                                 `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness meal plan relation ';


CREATE TABLE IF NOT EXISTS `proj_fitness_dish_collection` (
                                                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                              `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `calorie` decimal(8,
                      1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness dish collection ';

CREATE TABLE IF NOT EXISTS `proj_fitness_dish_collection_relation` (
                                                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                                       `proj_fitness_dish_collection_id` int NOT NULL COMMENT 'proj_fitness_dish_collection id',
                                                                       `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
                                                                       `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish id',
                                                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                       `proj_id` int NOT NULL COMMENT '项目id',
                                                                       `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness dish collection relation ';


CREATE TABLE IF NOT EXISTS `proj_fitness_meal_plan_pub` (
                                                            `version` int NOT NULL COMMENT '版本',
                                                            `id` int unsigned NOT NULL COMMENT 'id',
                                                            `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `days` int DEFAULT NULL COMMENT '难度',
    `calorie` decimal(8,
                      1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,
                 `id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness meal plan pub';

CREATE TABLE IF NOT EXISTS `proj_fitness_meal_plan_relation_pub` (
                                                                     `version` int NOT NULL COMMENT '版本',
                                                                     `id` int unsigned NOT NULL COMMENT 'id',
                                                                     `proj_fitness_meal_plan_id` int NOT NULL COMMENT 'proj_fitness_meal_plan id',
                                                                     `day` int NOT NULL COMMENT '表示一个MealPlan中第几天',
                                                                     `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
                                                                     `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish id',
                                                                     `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                     `proj_id` int NOT NULL COMMENT '项目id',
                                                                     `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,
                 `id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness meal plan relation pub';


CREATE TABLE IF NOT EXISTS `proj_fitness_dish_collection_pub` (
                                                                  `version` int NOT NULL COMMENT '版本',
                                                                  `id` int unsigned NOT NULL COMMENT 'id',
                                                                  `name` varchar(127) NOT NULL COMMENT '动作展示名称',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `event_name` varchar(127) NOT NULL COMMENT '流程名称',
    `cover_img_url` varchar(255) NOT NULL COMMENT '封面图',
    `detail_img_url` varchar(255) NOT NULL COMMENT '详情图',
    `calorie` decimal(8,
                      1) DEFAULT NULL COMMENT '卡路里',
    `description` varchar(511) DEFAULT NULL COMMENT '描述',
    `keywords` varchar(127) DEFAULT NULL COMMENT '标签值，以英文逗号做分隔，如A,B,C',
    `new_start_time` datetime DEFAULT NULL COMMENT 'new开始时间',
    `new_end_time` datetime DEFAULT NULL COMMENT 'new结束时间',
    `replacement_tag` tinyint DEFAULT NULL COMMENT '用于控制显示一个固定标签，如SNACK',
    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `proj_id` int NOT NULL COMMENT '项目id',
    `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,
                 `id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness dish collection pub';

CREATE TABLE IF NOT EXISTS `proj_fitness_dish_collection_relation_pub` (
                                                                           `version` int NOT NULL COMMENT '版本',
                                                                           `id` int unsigned NOT NULL COMMENT 'id',
                                                                           `proj_fitness_dish_collection_id` int NOT NULL COMMENT 'proj_fitness_dish_collection id',
                                                                           `dish_type` int NOT NULL COMMENT '美食分类，100:BREAKFAST,101：LUNCH，102:DINNER，103:MEAL_REPLACEMENT',
                                                                           `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish id',
                                                                           `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                                           `proj_id` int NOT NULL COMMENT '项目id',
                                                                           `create_user` varchar(63) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`version`,
                 `id`)
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = ' proj fitness dish collection relation pub';

-- 新增dish相关表
CREATE TABLE IF NOT EXISTS `proj_fitness_dish_pub` (
                                         `version` int NOT NULL COMMENT '版本',
                                         `id` int unsigned NOT NULL COMMENT 'id',
                                         `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                         `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'name',
                                         `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                         `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                         `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                         `types` varchar(255) DEFAULT NULL COMMENT '类型多选用英文逗号分隔，100:Breakfast,101:Lunch,102:Dinner,103:Meal Replacement',
                                         `styles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '风格，多选用英文逗号分隔100:Vegan,101:Mediterranean,102:Keto,103:Smoothie',
                                         `prepare_time` int DEFAULT NULL COMMENT '准备时间，单位分钟',
                                         `calorie` decimal(8,1) DEFAULT '0.0' COMMENT '卡路里',
                                         `carb` decimal(8,1) DEFAULT '0.0' COMMENT '碳水含量',
                                         `protein` decimal(8,1) DEFAULT '0.0' COMMENT '蛋白质含量',
                                         `fat` decimal(8,1) DEFAULT '0.0' COMMENT '脂肪含量',
                                         `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
                                         `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
                                         `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
                                         `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
                                         `serving` int DEFAULT NULL COMMENT '份数',
                                         `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                         `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                         `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish';


CREATE TABLE IF NOT EXISTS `proj_fitness_ingredient_pub` (
                                               `version` int NOT NULL COMMENT '版本',
                                               `id` int unsigned NOT NULL COMMENT 'id',
                                               `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                               `amount` varchar(127) DEFAULT NULL,
                                               `proj_fitness_unit_id` int DEFAULT NULL COMMENT 'proj_fitness_unit表数据id',
                                               `proj_fitness_dish_id` int NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                               `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                               `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                               `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                               `create_time` datetime NOT NULL COMMENT '创建时间',
                                               `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`version`,`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness ingredient';

CREATE TABLE IF NOT EXISTS `proj_fitness_unit_pub` (
                                         `version` int NOT NULL COMMENT '版本',
                                         `id` int unsigned NOT NULL COMMENT 'id',
                                         `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                         `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位名称',
                                         `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                         `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                         `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                         `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness_unit表';

CREATE TABLE IF NOT EXISTS `proj_fitness_dish_step_pub` (
                                              `version` int NOT NULL COMMENT '版本',
                                              `id` int unsigned NOT NULL COMMENT 'id',
                                              `description` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                                              `proj_fitness_dish_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                              `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                              `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                              `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                              PRIMARY KEY (`version`,`id`),
                                              KEY `idx_proj_fitness_dish_id` (`proj_fitness_dish_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish step';

CREATE TABLE IF NOT EXISTS `proj_fitness_dish_step_tip_pub` (
                                                  `version` int NOT NULL COMMENT '版本',
                                                  `id` int unsigned NOT NULL COMMENT 'id',
                                                  `proj_fitness_dish_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish表数据id',
                                                  `proj_fitness_dish_step_id` int unsigned NOT NULL COMMENT 'proj_fitness_dish_step表数据id',
                                                  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图片地址',
                                                  `intro` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '介绍',
                                                  `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                  `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                  `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                  PRIMARY KEY (`version`,`id`),
                                                  KEY `idx_proj_fitness_dish_id` (`proj_fitness_dish_id`),
                                                  KEY `idx_proj_fitness_dish_step_id` (`proj_fitness_dish_step_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Dish step tip';

CREATE TABLE IF NOT EXISTS `proj_fitness_allergen_pub` (
                                             `version` int NOT NULL COMMENT '版本',
                                             `id` int unsigned NOT NULL COMMENT 'id',
                                             `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                             `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '过敏源名称',
                                             `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                             `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                             `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                             `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Allergen';

CREATE TABLE IF NOT EXISTS `proj_fitness_allergen_relation_pub` (
                                                      `version` int NOT NULL COMMENT '版本',
                                                      `id` int unsigned NOT NULL COMMENT 'id',
                                                      `data_id` int NOT NULL COMMENT '业务表数据id',
                                                      `proj_fitness_allergen_id` int NOT NULL COMMENT 'proj_fitness_allergen表数据id',
                                                      `business_type` int NOT NULL COMMENT '业务type，1000:dish',
                                                      `proj_id` int NOT NULL COMMENT '项目id',
                                                      `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                      `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                      `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                      PRIMARY KEY (`version`,`id`) USING BTREE,
                                                      KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='fitness_allergen和业务表的关系';


# 200->104数据迁移,执行前检查projId
# SET @projId:=17;
INSERT INTO proj_fitness_dish_collection (id,name,table_code ,event_name,cover_img_url,detail_img_url,calorie,description,keywords,new_start_time,new_end_time,replacement_tag,subscription,sorted,status,del_flag,proj_id,create_user,create_time,update_user,update_time)
select id,name,8,event_name,cover_img_url,detail_img_url,calorie,description,keywords,new_start_time,new_end_time,replacement_tag,subscription,sorted,status,del_flag,@projId,create_user,create_time,update_user,update_time
from proj_dish_collection where del_flag = 0;

INSERT INTO proj_fitness_dish_collection_relation
(id, proj_fitness_dish_collection_id, dish_type, proj_fitness_dish_id, del_flag, proj_id, create_user, create_time, update_user, update_time)
select id,proj_dish_collection_id, dish_type, proj_dish_id, del_flag, @projId, create_user, create_time, update_user, update_time
from proj_dish_collection_relation where del_flag =0;

INSERT INTO proj_fitness_meal_plan
(id,name, table_code, event_name, cover_img_url, detail_img_url, days, calorie, description, keywords, new_start_time, new_end_time, replacement_tag, subscription, sorted, status, del_flag, proj_id, create_user, create_time, update_user, update_time)
select id,name, 9,event_name, cover_img_url, detail_img_url, days, calorie, description, keywords, new_start_time, new_end_time, replacement_tag, subscription, sorted, status, del_flag, @projId, create_user, create_time, update_user, update_time
from proj_meal_plan where del_flag = 0;

INSERT INTO proj_fitness_meal_plan_relation (id,proj_fitness_meal_plan_id,`day`,dish_type,proj_fitness_dish_id,del_flag,proj_id,create_user,create_time,update_user,update_time)
select id,proj_meal_plan_id, `day`, dish_type, proj_dish_id, del_flag, @projId, create_user, create_time, update_user, update_time
from proj_meal_plan_relation where del_flag = 0;


#菜单配置------
BEGIN;
SET @menuId = 0;
SET @operator = '<EMAIL>';

SET @menuName:='Fitness Dish';
SET @urlStart:='fitnessDish';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Meal Plan';
SET @urlStart:='fitnessMealPlan';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Dish Collection';
SET @urlStart:='fitnessDishCollection';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Fasting Article';
SET @urlStart:='fitnessFastingArticle';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Video Course';
SET @urlStart:='fitnessVideoCourse';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Coaching Courses';
set @urlStart:='fitnessCoachingCourses';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

SET @menuName:='Fitness Coach';
set @urlStart:='fitnessCoach';
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 19);
select last_insert_id() into @menuId;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'View', concat(@urlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'New', concat(@urlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Edit', concat(@urlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`) VALUES (null, @menuId, 'Del', concat(@urlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
COMMIT;


-- pub表

CREATE TABLE `proj_fitness_video_course_pub` (
                                                 `version` int NOT NULL COMMENT '版本',
                                                 `id` int unsigned NOT NULL COMMENT 'id',
                                                 `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                 `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                 `name` varchar(100) DEFAULT NULL COMMENT '名称',
                                                 `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
                                                 `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                                 `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                                 `play_type` int DEFAULT NULL COMMENT '视频宽高比',
                                                 `type` int DEFAULT NULL COMMENT 'Course类型',
                                                 `difficulty` int DEFAULT NULL COMMENT '难度',
                                                 `resource_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '源视频地址',
                                                 `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '多分辨率m3u8地址',
                                                 `video2532_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '2532 的m3u8地址',
                                                 `duration` int DEFAULT NULL COMMENT '视频时长，毫秒',
                                                 `calorie` int DEFAULT NULL COMMENT '卡路里',
                                                 `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                                 `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
                                                 `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
                                                 `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                                 `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                 `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                 PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_video_course_pub';


CREATE TABLE `proj_fitness_coach_pub` (
                                          `version` int NOT NULL COMMENT '版本',
                                          `id` int unsigned NOT NULL COMMENT 'id',
                                          `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                          `proj_id` int NOT NULL COMMENT 'project id',
                                          `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'coach name',
                                          `photo_img_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'photo_img_url',
                                          `introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Introduction',
                                          `status` tinyint NOT NULL DEFAULT '0' COMMENT '启用状态 1启用 2停用',
                                          `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                          `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                          `create_time` datetime NOT NULL COMMENT '创建时间',
                                          `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='proj_fitness_coach_pub';

CREATE TABLE `proj_fitness_coaching_courses_pub` (
                                                     `version` int NOT NULL COMMENT '版本',
                                                     `id` int unsigned NOT NULL COMMENT 'id',
                                                     `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                     `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                     `name` varchar(100) DEFAULT NULL COMMENT '名称',
                                                     `event_name` varchar(100) DEFAULT NULL COMMENT 'event name',
                                                     `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                                     `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                                     `age_groups` varchar(255) DEFAULT NULL COMMENT 'age_groups',
                                                     `types` varchar(255) DEFAULT NULL COMMENT 'types',
                                                     `difficulty` int DEFAULT NULL COMMENT '难度',
                                                     `description` varchar(1000) DEFAULT NULL COMMENT '描述',
                                                     `proj_fitness_coach_id` int DEFAULT NULL COMMENT '教练id',
                                                     `subscription` tinyint DEFAULT '0' COMMENT '是否收费 0不收费 1收费',
                                                     `new_start_time` datetime DEFAULT NULL COMMENT 'new 标签开始时间',
                                                     `new_end_time` datetime DEFAULT NULL COMMENT 'new 标签结束时间',
                                                     `status` tinyint DEFAULT '0' COMMENT '状态0 草稿、未启用 1 启用 2禁用',
                                                     `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                                     `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                     `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                     `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                     PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_coaching_courses_pub';


CREATE TABLE `proj_fitness_coaching_courses_relation_pub` (
                                                              `version` int NOT NULL COMMENT '版本',
                                                              `id` int unsigned NOT NULL COMMENT 'id',
                                                              `proj_fitness_video_course_id` int unsigned DEFAULT NULL COMMENT 'video course id',
                                                              `proj_fitness_coaching_courses_id` int unsigned DEFAULT NULL COMMENT 'coaching_courses_id',
                                                              `del_flag` tinyint DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                              `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                              `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                                              `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                              PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_coaching_courses_relation_pub';


CREATE TABLE `proj_fitness_fasting_article_pub` (
                                                    `version` int NOT NULL COMMENT '版本',
                                                    `id` int unsigned NOT NULL COMMENT 'id',
                                                    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
                                                    `title_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '动作展示名称',
                                                    `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'event名称',
                                                    `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '封面图',
                                                    `detail_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详情图',
                                                    `type` int DEFAULT NULL COMMENT '类型100:Fasting Basics,101:Fasting Hacks and Tips',
                                                    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
                                                    `reference` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考文档',
                                                    `proj_id` int unsigned NOT NULL COMMENT '项目id',
                                                    `subscription` tinyint DEFAULT NULL COMMENT '是否收费 0不收费 1收费',
                                                    `sorted` int NOT NULL DEFAULT '0' COMMENT '排序',
                                                    `status` tinyint DEFAULT '0' COMMENT '启用状态 0草稿 1启用 2停用',
                                                    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
                                                    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL COMMENT '创建时间',
                                                    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                                    PRIMARY KEY (`version`,`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='proj_fitness_fasting_article_pub';


alter table proj_fitness_coaching_courses
    add course_img_url varchar(255) null comment 'Home 竖版Image' after detail_img_url;
alter table proj_fitness_coaching_courses_pub
    add course_img_url varchar(255) null comment 'Home 竖版Image' after detail_img_url;



